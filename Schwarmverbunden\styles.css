/* Font Definitions */
@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    /* Optimize scrolling performance */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Lora', serif;
    line-height: 1.6;
    color: #fff8ef; /* Neue Farbe: Weiß */
    background-color: #29303b; /* Neue Farbe: Hellblau */
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
}

/* UNIFIED SPACING SYSTEM - Small, consistent spacing like the main title */
/* Base spacing units: 8px, 12px, 16px, 20px, 24px (much smaller!) */

/* Unified Heading System - Small, consistent spacing like section-main-title */
h1, h2, h3, h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin: 20px 0 12px 0; /* Viel kleiner! */
    line-height: 1.3;
}

h1 {
    font-size: 32px;
    color: #222;
    margin: 24px 0 16px 0; /* Wie section-main-title */
}

h2 {
    font-size: 28px;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin: 20px 0 12px 0; /* Kleiner */
}

h3 {
    font-size: 18px;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin: 16px 0 8px 0; /* Viel kleiner */
}

h4 {
    font-size: 16px;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin: 12px 0 8px 0; /* Sehr klein */
}



/* Banner */
.banner {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.banner h2 {
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    font-size: 2.5rem;
    z-index: 1;
    position: relative;
    text-shadow:
        0 0 10px rgba(4, 7, 15, 0.9), /* Neue Farbe: Dunkelblau */
        0 0 20px rgba(4, 7, 15, 0.7),
        2px 2px 4px rgba(4, 7, 15, 0.8);
    margin: 0;
}

.banner .einheitsbutton {
    z-index: 1;
    position: relative;
}

/* Banner content wrapper for perfect centering */
.banner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    z-index: 1;
    position: relative;
}

.banner-zwischenmenschlichkeit {
    background-image: url('assets/bilder/zwischenmenschlichkeit.png');
    background-size: cover !important;
    background-position: center 30% !important;
}

/* Scroll Indicator - Pfeil nach unten in Hero Bereichen */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.scroll-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounce 2s infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.arrow-symbol {
    color: #fbc99a;
    font-size: 28px;
    font-weight: 300;
    line-height: 0.6;
    transform: rotate(-90deg);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.arrow-symbol:first-child {
    margin-bottom: -8px;
    opacity: 0.7;
}

.arrow-symbol:last-child {
    opacity: 1;
}

.scroll-arrow:hover .arrow-symbol {
    color: #f59034;
}

.scroll-arrow:hover {
    transform: scale(1.1);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}

/* Desktop-spezifische Positionierung für Zwischenmenschlichkeit Banner */
@media (min-width: 769px) {
    .banner-zwischenmenschlichkeit {
        background-position: center 45% !important;
    }
}

.banner-zusammenhalt {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-fuehrung {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-blumenfeld {
    background-image: url('assets/bilder/blumenfeld.png');
}

.banner-weekend {
    background-image: url('assets/bilder/weekend.png');
}

.banner-zukunftinsjetzt {
    background-image: url('assets/bilder/zukunftinsjetzt.png');
}

.banner-event {
    background-image: url('assets/bilder/frauen.jpg');
}

.banner-jana {
    background-image: url('assets/bilder/steg.jpg');
}

/* Desktop-Styles für Banner-Titel - Vollbild Hero wie Mobile */
@media (min-width: 769px) {
    /* Desktop Hero Banner - Vollbild wie Mobile */
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
    }

    .banner-zukunftinsjetzt::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Desktop Hero Title - SCHWARMVERBUNDEN in Pfirsich */
    .mobile-hero-title {
        display: block !important;
        font-size: clamp(2rem, 8vw, 4rem) !important;
        color: #fbd19a !important; /* Pfirsich */
        margin: 0 20px 10px 20px !important;
        padding: 0 10px !important;
        z-index: 2 !important;
        position: relative !important;
        text-shadow:
            0 0 10px rgba(4, 7, 15, 0.8),
            0 0 20px rgba(4, 7, 15, 0.6),
            2px 2px 4px rgba(4, 7, 15, 0.9) !important;
        font-family: 'Montserrat', sans-serif !important;
        font-weight: 300 !important;
        letter-spacing: clamp(1px, 0.5vw, 3px) !important;
        text-align: center !important;
        max-width: 90% !important;
        box-sizing: border-box !important;
        white-space: nowrap !important;
    }

    /* Desktop Subtitle - verstecken, da wir ::after verwenden */
    .desktop-subtitle {
        display: none !important;
    }

    /* Desktop Untertitel für Hero Banner - CO-KREATION FÜR LIGHTWORKER in Weiß */
    .banner-zukunftinsjetzt::after {
        content: "CO-KREATION FÜR LIGHTWORKER";
        position: absolute;
        z-index: 2;
        color: #fff8ef; /* Weiß */
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: clamp(1rem, 3vw, 1.8rem);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 40px);
        text-align: center;
        padding: 0 20px;
        letter-spacing: clamp(0.5px, 0.3vw, 2px);
        text-shadow:
            0 0 8px rgba(4, 7, 15, 0.8),
            0 0 15px rgba(4, 7, 15, 0.6),
            1px 1px 3px rgba(4, 7, 15, 0.9);
        box-sizing: border-box;
        display: block !important;
    }

    /* Desktop Event Banner */
    .banner-event {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
    }

    .banner-event::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Desktop Untertitel für Event Banner */
    .banner-event::after {
        content: "CO-KREATION LIVE ERLEBEN \"SCHWARMKREIERT\"";
        position: absolute;
        z-index: 2;
        color: #fff8ef; /* Weiß */
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: clamp(1rem, 3vw, 1.8rem);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 40px);
        text-align: center;
        padding: 0 20px;
        letter-spacing: clamp(0.5px, 0.3vw, 2px);
        text-shadow:
            0 0 8px rgba(4, 7, 15, 0.8),
            0 0 15px rgba(4, 7, 15, 0.6),
            1px 1px 3px rgba(4, 7, 15, 0.9);
        box-sizing: border-box;
        display: block !important;
    }

    /* Desktop Jana Banner */
    .banner-jana {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
    }

    .banner-jana::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Desktop Untertitel für Jana Banner */
    .banner-jana::after {
        content: "JANA BREITMAR";
        position: absolute;
        z-index: 2;
        color: #fff8ef; /* Weiß */
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: clamp(1rem, 3vw, 1.8rem);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        width: calc(100% - 40px);
        text-align: center;
        padding: 0 20px;
        letter-spacing: clamp(0.5px, 0.3vw, 2px);
        text-shadow:
            0 0 8px rgba(4, 7, 15, 0.8),
            0 0 15px rgba(4, 7, 15, 0.6),
            1px 1px 3px rgba(4, 7, 15, 0.9);
        box-sizing: border-box;
        display: block !important;
    }
}


/* Unified Section Box - Small spacing like main title */
.section-box {
    max-width: 800px;
    margin: 20px auto; /* Viel kleiner! */
    padding: 20px; /* Viel kleiner! */
    background: #04070f; /* Neue Farbe: Dunkelblau */
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(251, 209, 154, 0.3); /* Neue Farbe: Pfirsich */
    line-height: 1.8;
    position: relative;
    text-align: center; /* Zentriere alle Texte */
}

.section-box p {
    margin-bottom: 12px; /* Kleiner! */
    font-size: 16px;
    color: #fff8ef; /* Neue Farbe: Weiß */
}

/* Reduziere Abstand zwischen Überschriften und nachfolgenden Listen */
/* Browserkompatible Lösung ohne :has() */
.section-box p.list-header {
    margin-bottom: 6px; /* Sehr klein */
}

/* Reduziere oberen Abstand der Listen nach list-header */
.section-box p.list-header + ul,
.section-box p.list-header + ol {
    margin-top: 6px; /* Sehr klein */
}

.section-box strong {
    font-weight: 700;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
}

.section-box h3 {
    text-align: center; /* Überschriften auch zentriert */
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    font-size: 20px;
    margin: 16px 0 8px 0; /* Viel kleiner! */
    font-weight: 600;
}

.section-box ul {
    margin: 12px 0; /* Kleiner */
    padding-left: 0;
    text-align: center; /* Aufzählungen zentriert */
    display: block; /* Volle Breite für zentrierte Darstellung */
    list-style: none;
}

.section-box ol {
    margin: 12px 0; /* Kleiner */
    padding-left: 0;
    text-align: center; /* Nummerierte Listen zentriert */
    display: block; /* Volle Breite für zentrierte Darstellung */
    list-style: none;
}

.section-box li {
    margin-bottom: 6px; /* Sehr klein */
    font-size: 16px;
    color: #fff8ef; /* Neue Farbe: Weiß */
    text-align: center;
    position: relative;
    padding: 0;
}

/* Modern list styling - Small spacing */
.possibilities-list {
    list-style: none;
    padding-left: 0;
    text-align: left;
    display: inline-block; /* Macht die Liste nur so breit wie nötig */
    max-width: 600px; /* Begrenzt die maximale Breite */
    margin: 6px auto;
}

.possibilities-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 6px;
    line-height: 1.6;
    text-align: left;
}

.possibilities-list li::before {
    content: "✦";
    position: absolute;
    left: 0;
    top: 0;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    font-size: 16px;
}

/* Modern Accordion Styling - Small spacing */
.accordion-container {
    margin: 12px auto;
    max-width: 800px;
}

.accordion-item {
    margin-bottom: 6px;
    border-radius: 16px;
    overflow: hidden;
    background: rgba(41, 48, 59, 0.9);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease;
}

.accordion-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 40px rgba(251, 201, 154, 0.15);
    border-color: rgba(251, 201, 154, 0.3);
}

.accordion-header {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    background: transparent;
    transition: all 0.4s ease;
    user-select: none;
    border: none;
    position: relative;
}

.accordion-header:hover {
    background: rgba(251, 201, 154, 0.05);
}

.accordion-header.active {
    background: rgba(251, 201, 154, 0.08);
}

.day-number {
    font-weight: 400;
    color: #fbc99a;
    min-width: 120px;
    font-size: 16px;
    letter-spacing: 2px;
    text-align: left;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.day-focus {
    flex: 1;
    margin-left: 12px; /* Kleiner! */
    font-weight: 500;
    color: #ffffff;
    font-size: 15px;
    text-align: left;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.accordion-icon {
    font-size: 18px;
    font-weight: bold;
    color: #fbc99a;
    transition: transform 0.3s ease;
    min-width: 25px;
    text-align: center;
    position: absolute;
    right: 20px; /* Angepasst an neues Padding */
    top: 50%;
    transform: translateY(-50%);
}

.accordion-header.active .accordion-icon {
    transform: translateY(-50%) rotate(45deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: rgba(15, 21, 26, 0.8);
    backdrop-filter: blur(5px);
}

.accordion-content.active {
    max-height: 300px;
}

.accordion-content p {
    padding: 16px; /* Kleiner! */
    margin: 0;
    line-height: 1.7;
    color: #e8e8e8;
    font-size: 15px;
    text-align: left;
    border-top: 1px solid rgba(251, 201, 154, 0.2);
}

.facts-list {
    list-style: none;
    padding-left: 0;
    text-align: left;
    display: inline-block; /* Macht die Liste nur so breit wie nötig */
    max-width: 600px; /* Begrenzt die maximale Breite */
    margin: 12px auto; /* Kleiner! */
}

.facts-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 6px; /* Kleiner! */
    line-height: 1.6;
    text-align: left;
}

.facts-list li::before {
    content: "▸";
    position: absolute;
    left: 0;
    top: 0;
    color: #04070f;
    font-size: 16px;
    font-weight: bold;
}

/* Modern Slideshow Container */
.slideshow-container {
    position: relative;
    max-width: 800px;
    margin: 12px auto; 
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.slideshow-wrapper {
    position: relative;
    width: 100%;
    height: 300px; /* Feste Höhe für alle Slides */
    overflow: hidden;
    touch-action: pan-y pinch-zoom;
}

.slides-track {
    display: flex;
    width: 600%; /* 6 Slides * 100% */
    height: 100%;
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide {
    flex: 0 0 calc(100% / 6); /* Jeder Slide nimmt 1/6 der Breite */
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* Versteckte externe Navigation */
.slideshow-nav {
    display: none;
}

/* Navigation außerhalb der Slide-Kästchen */
.slide-nav-left,
.slide-nav-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid #29303b;
    border-radius: 50%;
    color: #29303b;
    font-size: 18px;
    font-weight: 300;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.slide-nav-left {
    left: -60px; /* Außerhalb des Slide-Kästchens */
}

.slide-nav-right {
    right: -60px; /* Außerhalb des Slide-Kästchens */
}

.slide-nav-left:hover,
.slide-nav-right:hover {
    background: #29303b;
    color: #fff8ef;
    border-color: #29303b;
}

/* Dezente Indikatoren im Textkästchen */
.slide-indicators-internal {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 10;
}

.indicator-internal {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(41, 48, 59, 0.3); /* Dunkelblau */
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator-internal.active {
    background: #29303b; /* Dunkelblau */
    transform: scale(1.3);
}

.indicator-internal:hover:not(.active) {
    background: rgba(41, 48, 59, 0.6); /* Dunkelblau */
}

/* Highlight Boxes - Small spacing */
.highlight-box {
    background: transparent; /* Weißer/transparenter Hintergrund */
    padding: 16px; /* Kleiner! */
    margin: 0;
    border-radius: 12px;
    box-shadow: none; /* Schatten entfernt */
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.highlight-box p {
    margin: 0;
    line-height: 1.6;
    text-align: left;
    color: #29303b !important; /* Dunkelblauer Text */
    font-size: 16px;
}

.highlight-box strong {
    color: #29303b !important; /* Dunkelblauer Text */
    font-weight: 600;
    display: block;
    margin-bottom: 6px; /* Kleiner! */
    font-size: 18px;
}

/* Facts Box - für praktische Informationen - angepasst für Events Section */
.facts-box {
    background: linear-gradient(135deg, #fff8ef 0%, #29303b 100%);
    padding: 24px; /* Kompakter */
    margin: 16px auto; /* Kompakter */
    border-radius: 8px;
    box-shadow: none;
    max-width: 800px;
}

.facts-box .facts-list {
    margin: 0;
}

.facts-list li {
    color: #04070f;
}

.facts-list li::before {
    color: #04070f;
}

/* Possibilities Box - für "Was du erleben kannst" Liste - angepasst für Events Section */
.possibilities-box {
    background: linear-gradient(135deg, #fff8ef 0%, #29303b 100%);
    padding: 24px; /* Kompakter */
    margin: 16px auto; /* Kompakter */
    border-radius: 8px;
    box-shadow: none;
    max-width: 800px;
}

.possibilities-box .possibilities-list {
    margin: 0;
}

.possibilities-list li {
    color: #04070f;
}

.possibilities-list li::before {
    color: #04070f;
}

/* Principles Boxes - für Schwarmverbindung Prinzipien */
.principles-box {
    background: rgba(251, 201, 154, 0.05);
    border: 2px solid rgba(251, 201, 154, 0.3);
    padding: 24px; /* Kompakter */
    margin: 16px 0; /* Kompakter */
    border-radius: 12px;
}

.principles-box p {
    margin-bottom: 16px; /* Konsistent */
    line-height: 1.7;
}

.principles-box p:last-child {
    margin-bottom: 0;
}

/* Kompakte Flip Cards - 2x4 Grid Layout */
.flip-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px; /* Konsistent mit System */
    margin: 16px 0; /* Kompakter */
}

.flip-card-compact {
    background-color: transparent;
    width: 100%;
    height: 160px;
    perspective: 1000px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.5s;
    transform-style: preserve-3d;
    cursor: pointer;
}

.flip-card-compact:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px; /* Konsistent */
    box-sizing: border-box;
}

/* Flip Cards - angepasst für dunklen Hintergrund */
.zwischenmenschlichkeit-content-section .flip-card-front {
    background: rgba(251, 209, 154, 0.1); /* Neue Farbe: Pfirsich */
    border: 1px solid rgba(251, 209, 154, 0.3);
    color: #fff8ef; /* Neue Farbe: Weiß */
}

.zwischenmenschlichkeit-content-section .flip-card-back {
    background: rgba(251, 209, 154, 0.15); /* Neue Farbe: Pfirsich */
    border: 1px solid rgba(251, 209, 154, 0.4);
    color: #fff8ef; /* Neue Farbe: Weiß */
    transform: rotateY(180deg);
}

.zwischenmenschlichkeit-content-section .flip-card-front h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 10px;
    font-weight: 600;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin: 0;
    line-height: 1.1;
    text-align: center;
}

.zwischenmenschlichkeit-content-section .flip-card-back p {
    font-size: 12px;
    line-height: 1.3;
    margin: 0;
    color: #fff8ef; /* Neue Farbe: Weiß */
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

/* Standard Flip Cards für andere Bereiche */
.flip-card-front {
    background: rgba(251, 209, 154, 0.1); /* Neue Farbe: Pfirsich */
    border: 1px solid rgba(251, 209, 154, 0.3);
    color: #fff8ef; /* Neue Farbe: Weiß */
}

.flip-card-back {
    background: rgba(251, 209, 154, 0.15); /* Neue Farbe: Pfirsich */
    border: 1px solid rgba(251, 209, 154, 0.4);
    color: #fff8ef; /* Neue Farbe: Weiß */
    transform: rotateY(180deg);
}

.flip-card-front h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 10px;
    font-weight: 600;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin: 0;
    line-height: 1.1;
    text-align: center;
}

.flip-card-back p {
    font-size: 12px;
    line-height: 1.3;
    margin: 0;
    color: #fff8ef; /* Neue Farbe: Weiß */
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
}

/* h4 Styling für Prinzipien-Kategorien */
.section-box h4 {
    font-family: 'Montserrat', sans-serif;
    font-size: 18px;
    font-weight: 600;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    text-align: center;
    margin: 12px 0 8px 0; /* Viel kleiner! */
}

.section-box h4:first-of-type {
    margin-top: 8px; /* Viel kleiner! */
}

/* Call to Action Box - poetischer, inspirierender Stil */
.call-to-action-box {
    background: linear-gradient(135deg, rgba(251, 201, 154, 0.15), rgba(251, 201, 154, 0.08));
    border: none;
    padding: 20px; /* Viel kleiner! */
    margin: 16px 0; /* Kleiner! */
    border-radius: 20px;
    text-align: center;
    position: relative;
    box-shadow: 0 8px 25px rgba(251, 201, 154, 0.2);
}

/* Peach Card - Pfirsichfarbene Karte */
.peach-card {
    background: #fbd19a;
    padding: 20px; /* Viel kleiner! */
    margin: 16px auto; /* Kleiner! */
    border-radius: 15px;
    max-width: 800px;
    box-shadow: 0 8px 25px rgba(251, 209, 154, 0.3);
    text-align: center;
    position: relative;
}

.peach-card p {
    margin-bottom: 16px; /* Konsistent */
    line-height: 1.7;
    font-size: 16px;
    color: #38160e;
}

.peach-card p:last-child {
    margin-bottom: 0;
}

.peach-card strong {
    color: #38160e;
    font-weight: 600;
}

.call-to-action-box::before {
    content: "✦";
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 20px;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    opacity: 0.7;
}

.call-to-action-box p {
    margin-bottom: 16px; /* Konsistent */
    line-height: 1.8;
    font-size: 16px;
}

.call-to-action-box p:last-child {
    margin-bottom: 0;
}

.call-emphasis {
    font-size: 20px !important;
    color: #fbd19a !important; /* Neue Farbe: Pfirsich */
    margin: 24px 0 !important; /* Kompakter */
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 600 !important;
    letter-spacing: 1px;
}

.call-final {
    font-style: italic !important;
    font-size: 17px !important;
    margin-top: 24px !important; /* Kompakter */
    color: #fbd19a !important; /* Neue Farbe: Pfirsich */
}

/* Community Section - Small spacing like main title */
.community-section {
    background-color: #04070f; /* Bleibt Dunkelblau */
    padding: 24px 20px; /* Noch kleiner! */
    margin: 0;
    width: 100%;
}

.section-main-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    text-align: center;
    margin: 0 auto 24px auto; /* Perfekt - bleibt so! */
    max-width: 800px;
    letter-spacing: 1px;
    line-height: 1.2;
}

.community-content {
    max-width: 800px;
    margin: 0 auto;
    color: #fff8ef;
    line-height: 1.8;
    text-align: center;
}

.community-content p {
    margin-bottom: 12px; /* Kleiner! */
    font-size: 16px;
    color: #fff8ef;
}

.letter-body {
    margin-bottom: 16px; /* Kleiner! */
}

.letter-body p {
    margin-bottom: 12px; /* Kleiner! */
    line-height: 1.8;
    color: #fff8ef;
}

.letter-action {
    text-align: center;
    margin-top: 16px; /* Kleiner! */
}

/* Brief-Styling für andere Bereiche */
.letter-greeting {
    margin-bottom: 16px; /* Kleiner! */
}

.letter-greeting p {
    font-size: 18px;
    font-style: italic;
    color: #fff8ef;
    font-weight: 500;
}

.letter-vision {
    margin: 24px 0; /* Kompakter */
}

.letter-vision p {
    margin-bottom: 16px; /* Konsistent */
    line-height: 1.7;
}

.letter-emphasis {
    font-weight: 600;
    color: #fff8ef;
}

.letter-signature {
    margin: 24px 0; /* Kompakter und symmetrisch */
    text-align: center;
    font-style: italic;
    color: #fff8ef;
}

/* Community Section - now uses general centered styling from .section-box */

/* Profile elements in section-box - centered styling */
.section-box .profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 2px solid #fbd19a; /* Neue Farbe: Pfirsich */
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin: 0 auto 20px auto;
    display: block;
}

.section-box .profile-image:hover {
    transform: scale(1.05);
}



/* Center social links in section-box */
.section-box .social-links {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Center einheitsbutton for social links */
.social-links .einheitsbutton {
    margin: 0 !important;
    display: inline-block !important;
    width: auto !important;
    flex-shrink: 0;
}

/* Letter Closing Banner/Pre-Footer */
.letter-closing-banner {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
    position: relative;
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
}

/* Dekoratives Favicon-Element entfernt - wird jetzt durch Transition-Container ersetzt */

.letter-closing-banner .closing-content {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 40px;
    align-items: center;
    max-width: 800px;
    width: 100%;
}

.letter-closing-banner .closing-profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    justify-self: center;
}

.letter-closing-banner .closing-profile-image:hover {
    transform: scale(1.05);
}

.letter-closing-banner .closing-text {
    text-align: center;
}

.letter-closing-banner .closing-text .letter-signature {
    font-size: 28px;
    font-style: italic;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    margin-bottom: 16px; /* Kleiner! */
    font-weight: 300;
}

.letter-closing-banner .closing-text h3 {
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    font-size: 22px;
    margin: 0 0 25px 0;
    font-weight: 600;
}

/* About Me Link - klein und unaufdringlich */
.letter-closing-banner .about-me-link {
    display: inline-block;
    color: #8b6c5f; /* Dezente braune Farbe aus der Farbpalette */
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    margin: 10px 0 15px 0;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.letter-closing-banner .about-me-link:hover {
    color: #fbd19a; /* Pfirsich beim Hover */
    opacity: 1;
    text-decoration: underline;
}

.letter-closing-banner .closing-text .social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Social Media Buttons für hellen Letter Closing Banner */
.letter-closing-banner .einheitsbutton.instagram {
    background-color: transparent !important;
    border: 2px solid #e91e63 !important;
    color: #e91e63 !important;
}

.letter-closing-banner .einheitsbutton.instagram:hover {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
    border: 2px solid transparent !important;
    color: white !important;
    transform: scale(1.05);
}

.letter-closing-banner .einheitsbutton.spotify {
    background-color: transparent !important;
    border-color: #1DB954 !important;
    color: #1DB954 !important;
}

.letter-closing-banner .einheitsbutton.spotify:hover {
    background-color: #1DB954 !important;
    border-color: #1DB954 !important;
    color: #04070f !important; /* Neue Farbe: Dunkelblau */
    transform: scale(1.05);
}

.letter-closing-banner .einheitsbutton.telegram {
    background-color: transparent !important;
    border-color: #0088cc !important;
    color: #0088cc !important;
}

.letter-closing-banner .einheitsbutton.telegram:hover {
    background-color: #0088cc !important;
    border-color: #0088cc !important;
    color: #04070f !important; /* Neue Farbe: Dunkelblau */
    transform: scale(1.05);
}

/* Einheitsbutton - Unified button styling for all buttons */
.einheitsbutton {
    display: block;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: auto;
    width: fit-content;
    text-align: center;
    background-color: #fbd19a; /* Neue Farbe: Pfirsich */
    color: #04070f; /* Neue Farbe: Dunkelblau */
    border: 2px solid #fbd19a;
}

/* Standard hover animation - color inversion + scale */
.einheitsbutton:hover {
    background-color: #04070f; /* Neue Farbe: Dunkelblau */
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    border-color: #fbd19a;
    transform: scale(1.05);
}

/* Inactive extension - overrides default active state */
.einheitsbutton.inactive {
    background-color: #fbd19a;
    color: #04070f;
    border: 2px solid #fbd19a;
    opacity: 1;
    pointer-events: none;
    cursor: not-allowed;
}

.einheitsbutton.inactive:hover {
    background-color: #fbd19a;
    color: #04070f;
    border: 2px solid #fbd19a;
    transform: none;
}

/* Transparent extension - for banner buttons */
.einheitsbutton.transparent {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.einheitsbutton.transparent:hover {
    background-color: white;
    color: #04070f; /* Neue Farbe: Dunkelblau */
    border-color: white;
    transform: scale(1.05);
}

/* Events Text - simple text without box styling */
.events-text {
    max-width: 800px;
    margin: 20px auto 12px auto; /* Viel kleiner! */
    padding: 0 30px;
    text-align: center;
    line-height: 1.8;
}

.events-text p {
    margin-bottom: 12px; /* Kleiner! */
    font-size: 16px;
}

/* Events Grid Container - separate container for better width utilization */
.events-grid-container {
    max-width: 1200px;
    margin: 12px auto 20px auto; /* Kleiner! */
    padding: 0 30px;
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    justify-content: center;
    max-width: 100%;
}

/* Limit to maximum 3 columns */
@media (min-width: 1020px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 380px));
    }
}

/* For very wide screens, allow even more width */
@media (min-width: 1400px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 420px));
    }
}

.event-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* Optimize for scrolling performance */
    will-change: auto;
}

.event-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.event-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}


.event-description {
    font-size: 14px;
    margin-bottom: 15px;
    color: #fff8ef; /* Neue Farbe: Weiß */
    flex-grow: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 18px;
    margin-top: 25px;
    flex-wrap: nowrap;
    align-items: center;
    overflow: hidden;
}

/* Instagram extension - special gradient styling */
.einheitsbutton.instagram {
    border: 2px solid transparent;
    background: linear-gradient(#04070f, #04070f) padding-box, /* Neue Farbe: Dunkelblau */
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: #e6683c;
}

.einheitsbutton.instagram:hover {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: white;
    transform: scale(1.05);
}

/* YouTube extension - red styling */
.einheitsbutton.youtube {
    background-color: #04070f; /* Neue Farbe: Dunkelblau */
    border-color: #FF0000;
    color: #FF0000;
}

.einheitsbutton.youtube:hover {
    background-color: #FF0000;
    border-color: #FF0000;
    color: #04070f; /* Neue Farbe: Dunkelblau */
    transform: scale(1.05);
}

/* Spotify extension - green styling */
.einheitsbutton.spotify {
    background-color: #04070f; /* Neue Farbe: Dunkelblau */
    border-color: #1DB954;
    color: #1DB954;
}

.einheitsbutton.spotify:hover {
    background-color: #1DB954;
    border-color: #1DB954;
    color: #04070f; /* Neue Farbe: Dunkelblau */
    transform: scale(1.05);
}

/* Telegram extension - blue styling */
.einheitsbutton.telegram {
    background-color: #04070f; /* Neue Farbe: Dunkelblau */
    border-color: #0088cc;
    color: #0088cc;
}

.einheitsbutton.telegram:hover {
    background-color: #0088cc;
    border-color: #0088cc;
    color: #04070f; /* Neue Farbe: Dunkelblau */
    transform: scale(1.05);
}

/* Events Image Section - Horizontales Bild mit Farbwechsel */
.events-image-section {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.events-horizontal-image {
    width: 100%;
    max-width: 1120px; /* +40% von 800px für Desktop - gleich wie event-transition-image */
    height: 360px; /* +20% von 300px - gleich wie event-transition-image */
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 16px; /* Kleiner! */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.events-title {
    color: #fbd19a !important;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    max-width: 800px;
    margin: 0 20px;
    text-shadow: none;
}

/* Events Content Section - Small spacing */
.events-content-section {
    background-color: #fff8ef;
    padding: 24px 20px; /* Noch kleiner! */
    margin: 0;
    width: 100%;
    text-align: center; /* Zentriert alle Inhalte */
}

.events-content-section p {
    max-width: 800px;
    margin: 0 auto 12px auto; /* Kleiner! */
    color: #04070f;
    font-size: 16px;
    line-height: 1.8;
    text-align: center;
}

.events-content-section h3 {
    max-width: 800px;
    margin: 16px auto 8px auto; /* Viel kleiner! */
    color: #04070f;
    text-align: center;
}

.events-content-section h3:first-child {
    margin-top: 0;
}

.events-content-section strong {
    color: #04070f;
}

/* Listen in Events Content Section - zentriert aber linksbündig */
.events-content-section .possibilities-list,
.events-content-section .facts-list {
    display: inline-block;
    text-align: left;
    max-width: 600px;
    margin: 16px auto; /* Konsistent */
}

.events-content-section .possibilities-list li,
.events-content-section .facts-list li {
    color: #04070f;
}

.events-content-section .possibilities-list li::before {
    color: #fbd19a; /* Pfirsich für Sterne */
}

.events-content-section .facts-list li::before {
    color: #04070f; /* Dunkelblau für Pfeile */
}

/* Zwischenmenschlichkeit Image Section - Horizontales Bild mit Farbwechsel */
.zwischenmenschlichkeit-image-section {
    position: relative;
    width: 100%;
    margin: 0;
    padding: 0;
    background: linear-gradient(to bottom, #fff8ef 50%, #04070f 50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.zwischenmenschlichkeit-horizontal-image {
    width: 100%;
    max-width: 1120px; /* +40% von 800px für Desktop - gleich wie event-transition-image */
    height: 360px; /* +20% von 300px - gleich wie event-transition-image */
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 16px; /* Kleiner! */
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.zwischenmenschlichkeit-title {
    color: #fbd19a !important;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    max-width: 800px;
    margin: 0 20px;
    text-shadow: none;
}

/* Zwischenmenschlichkeit Content Section - Small spacing */
.zwischenmenschlichkeit-content-section {
    background-color: #04070f;
    padding: 16px 20px 24px 20px; /* Noch kleiner! */
    margin: 0;
    width: 100%;
}

.zwischenmenschlichkeit-content-section p {
    max-width: 800px;
    margin: 0 auto 12px auto; /* Kleiner! */
    color: #fff8ef;
    font-size: 16px;
    line-height: 1.8;
    text-align: center;
}

.zwischenmenschlichkeit-content-section h3 {
    max-width: 800px;
    margin: 16px auto 8px auto; /* Viel kleiner! */
    color: #fbd19a;
    text-align: center;
}

.zwischenmenschlichkeit-content-section h3:first-child {
    margin-top: 0;
}

.zwischenmenschlichkeit-content-section strong {
    color: #fbd19a;
}

/* Community Button Section - nach Flipcards */
.community-button-section {
    text-align: center;
    margin-top: 20px; /* Viel kleiner! */
    padding: 16px 0; /* Kleiner! */
}

.community-button-section .einheitsbutton {
    display: inline-block;
    margin: 0 auto;
}

/* Weekend Page Specific Styles */
.highlights-box {
    background-color: #f8f9fa;
    border-left: 4px solid #fbd19a; /* Neue Farbe: Pfirsich */
    padding: 16px; /* Kleiner! */
    margin: 16px 0; /* Viel kleiner! */
    border-radius: 5px;
}

.signature {
    text-align: right;
    margin-top: 20px; /* Viel kleiner! */
    font-style: italic;
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    font-size: 18px;
}

.signature a {
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    text-decoration: none;
    font-style: italic;
}

.signature a:hover {
    color: #fbd19a; /* Neue Farbe: Pfirsich */
    text-decoration: none;
}

/* Impressum Page Specific Styles - now uses section-box styling */
.section-box h2 {
    margin: 20px 0 12px 0; /* Wie globales System! */
    border-bottom: 2px solid #fbd19a; /* Neue Farbe: Pfirsich */
    padding-bottom: 8px; /* Kleiner */
}







/* Scrolling Performance Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
}

/* Smooth scrollbar styling for better UX */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: #29303b;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(251, 209, 154, 0.6); /* Neue Farbe: Pfirsich */
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(251, 209, 154, 0.8); /* Neue Farbe: Pfirsich */
}

/* Mobile-specific optimizations - disable hover effects for better performance */
@media (hover: none) and (pointer: coarse) {
    .einheitsbutton:hover,
    .event-card:hover,
    .profile-image:hover {
        transform: none !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        background-color: inherit !important;
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    /* Prevent horizontal overflow on mobile */
    html, body {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Unified heading system - mobile adjustments */
    h1 {
        font-size: 26px;
    }

    h2 {
        font-size: 24px;
    }

    h3 {
        font-size: 16px;
    }

    h4 {
        font-size: 14px;
    }

    .banner h2 {
        font-size: 1.8rem;
    }

    .section-box {
        margin: 16px; /* Konsistent */
        padding: 16px; /* Konsistent */
    }

    /* Community Section Mobile */
    .community-section {
        padding: 24px 15px; /* Kleiner! */
    }

    .section-main-title {
        font-size: 1.8rem;
        margin-bottom: 20px; /* Kleiner! */
        padding: 0 10px;
        line-height: 1.3;
    }

    .community-content {
        padding: 0 8px; /* Konsistent */
    }

    /* Events Section Mobile */
    .events-image-section {
        min-height: 350px;
        padding: 12px 15px; /* Kleiner! */
    }

    .events-horizontal-image {
        height: 240px; /* +20% von 200px - gleich wie event-transition-image */
        margin-bottom: 12px; /* Kleiner! */
    }

    .events-title {
        font-size: 1.6rem;
        padding: 12px 20px; /* Kleiner! */
        margin: 0 10px;
        line-height: 1.3;
    }

    .events-content-section {
        padding: 24px 15px; /* Kleiner! */
    }

    /* Slideshow Mobile */
    .slideshow-container {
        margin: 12px auto; /* Kleiner! */
        border-radius: 8px;
    }

    .slideshow-wrapper {
        height: 280px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: 36px;
        height: 36px;
        font-size: 16px;
        background: transparent;
        border: 1px solid #29303b;
        color: #29303b;
    }

    .slide-nav-left {
        left: -50px; /* Außerhalb des Slide-Kästchens */
    }

    .slide-nav-right {
        right: -50px; /* Außerhalb des Slide-Kästchens */
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        background: #29303b;
        color: #fff8ef;
    }

    .slide-indicators-internal {
        bottom: 15px;
    }

    .indicator-internal {
        width: 5px;
        height: 5px;
        background: rgba(41, 48, 59, 0.3); /* Dunkelblau */
    }

    .indicator-internal.active {
        background: #29303b; /* Dunkelblau */
    }

    .indicator-internal:hover:not(.active) {
        background: rgba(41, 48, 59, 0.6); /* Dunkelblau */
    }

    .highlight-box {
        padding: 16px; /* Kleiner! */
        border-radius: 8px;
        background: transparent; /* Weißer/transparenter Hintergrund */
        box-shadow: none; /* Schatten entfernt */
    }

    .highlight-box strong {
        font-size: 16px;
        margin-bottom: 6px; /* Kleiner! */
        color: #29303b !important; /* Dunkelblauer Text */
    }

    .highlight-box p {
        font-size: 15px;
        color: #29303b !important; /* Dunkelblauer Text */
    }

    /* Zwischenmenschlichkeit Section Mobile */
    .zwischenmenschlichkeit-image-section {
        min-height: 350px;
        padding: 16px 15px; /* Kompakter */
    }

    .zwischenmenschlichkeit-horizontal-image {
        height: 240px; /* +20% von 200px - gleich wie event-transition-image */
        margin-bottom: 16px; /* Konsistent */
    }

    .zwischenmenschlichkeit-title {
        font-size: 1.6rem;
        padding: 0;
        margin: 0 10px;
        line-height: 1.3;
    }

    .zwischenmenschlichkeit-content-section {
        padding: 16px 15px 32px 15px; /* Kompakter */
    }

    .events-text {
        margin: 20px;
        padding: 0 20px;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .banner {
        height: 300px;
    }

    /* Mobile-specific banner positioning for better image cropping */
    .banner.banner-zusammenhalt {
        background-size: 200%;
        background-position: center 55%;
    }

    /* Mobile Hero Banner - Vollbild mit zentriertem Text */
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-zukunftinsjetzt::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Mobile Hero Title - SCHWARMVERBUNDEN */
    .mobile-hero-title {
        display: block !important;
        font-size: clamp(1.2rem, 6vw, 2.5rem) !important;
        color: #fbd19a !important;
        margin: 0 20px 5px 20px !important;
        padding: 0 10px !important;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8) !important;
        z-index: 2 !important;
        position: relative !important;
        font-family: 'Montserrat', sans-serif !important;
        font-weight: 600 !important;
        letter-spacing: clamp(0.3px, 0.5vw, 1.5px) !important;
        text-align: center !important;
        width: calc(100% - 40px) !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        white-space: nowrap !important;
    }

    /* Desktop Subtitle - verstecken auf mobil */
    .desktop-subtitle {
        display: none !important;
    }

    /* Untertitel für mobile Hero Banner */
    .banner-zukunftinsjetzt::after {
        content: "CO-KREATION FÜR LIGHTWORKER";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-size: 1.1rem;
        font-weight: 400;
        text-align: center;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        letter-spacing: 0.5px;
        width: calc(100% - 40px);
        max-width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        word-wrap: break-word;
    }

    /* Mobile Event Banner */
    .banner-event {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-event::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Untertitel für mobile Event Banner */
    .banner-event::after {
        content: "CO-KREATION LIVE ERLEBEN \"SCHWARMKREIERT\"";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-size: 1.1rem;
        font-weight: 400;
        text-align: center;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
        top: 55%;
        left: 50%;
        transform: translateX(-50%);
        letter-spacing: 0.5px;
        width: calc(100% - 40px);
        max-width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        word-wrap: break-word;
    }

    .section-box .profile-image {
        width: 150px;
        height: 150px;
    }

    /* Mobile Jana Banner */
    .banner-jana {
        height: 100vh !important;
        width: 100vw !important;
        background-size: cover !important;
        background-position: center !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        text-align: center !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .banner-jana::before {
        background: rgba(0, 0, 0, 0.3) !important;
    }

    /* Untertitel für mobile Jana Banner */
    .banner-jana::after {
        content: "JANA BREITMAR";
        position: absolute;
        z-index: 2;
        color: #fff8ef;
        font-family: 'Montserrat', sans-serif;
        font-weight: 400;
        font-size: 1rem;
        top: 53%;
        left: 50%;
        transform: translateX(-50%);
        letter-spacing: 0.5px;
        width: calc(100% - 40px);
        max-width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        word-wrap: break-word;
    }

    /* Mobile responsiveness for letter closing banner */
    .letter-closing-banner {
        height: 450px; /* Mehr Höhe für bessere Verteilung */
    }

    /* Runder Favicon entfernt */

    .letter-closing-banner {
        padding: 40px 20px; /* Normales Padding ohne Favicon */
    }

    .letter-closing-banner .closing-content {
        grid-template-columns: 1fr;
        gap: 30px; /* Mehr Abstand zwischen Elementen */
        text-align: center;
        padding: 0;
    }

    .letter-closing-banner .closing-profile-image {
        width: 140px;
        height: 140px;
        justify-self: center;
    }

    .letter-closing-banner .closing-text .letter-signature {
        font-size: 22px;
        margin-bottom: 20px;
    }

    .letter-closing-banner .closing-text h3 {
        font-size: 18px;
        margin-bottom: 20px;
    }

    /* About Me Link - mobile */
    .letter-closing-banner .about-me-link {
        font-size: 12px;
        margin: 8px 0 12px 0;
    }

    .letter-closing-banner .closing-text .social-links {
        gap: 8px;
        margin-bottom: 20px; /* Abstand zum unteren Rand */
    }

    .social-links {
        flex-direction: row;
        align-items: flex-start;
        gap: 18px;
    }

    .social-links .einheitsbutton {
        font-size: 12px !important;
        padding: 8px 15px !important;
        flex-shrink: 1;
        min-width: 0;
    }

    .highlights-box,
    .details-box,
    .participation-box {
        margin: 20px 0;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    /* Unified heading system - small mobile adjustments */
    h1 {
        font-size: 22px;
    }

    h2 {
        font-size: 20px;
    }

    h3 {
        font-size: 14px;
    }

    h4 {
        font-size: 13px;
    }

    .banner h2 {
        font-size: 1.5rem;
    }

    .section-box {
        margin: 8px; /* Konsistent */
        padding: 16px; /* Konsistent */
    }

    /* Community Section für sehr kleine Bildschirme */
    .community-section {
        padding: 24px 10px; /* Kompakter */
    }

    .section-main-title {
        font-size: 1.5rem;
        margin-bottom: 16px; /* Kompakter */
        padding: 0 8px; /* Konsistent */
    }

    /* Events Section für sehr kleine Bildschirme */
    .events-image-section {
        min-height: 300px;
        padding: 16px 10px; /* Konsistent */
    }

    .events-horizontal-image {
        height: 180px; /* +20% von 150px - gleich wie event-transition-image */
        margin-bottom: 16px; /* Konsistent */
    }

    .events-title {
        font-size: 1.3rem;
        padding: 16px; /* Konsistent */
        margin: 0 8px; /* Konsistent */
        line-height: 1.2;
    }

    .events-content-section {
        padding: 24px 10px; /* Kompakter */
    }

    /* Slideshow für sehr kleine Bildschirme */
    .slideshow-container {
        margin: 10px auto;
        border-radius: 6px;
    }

    .slideshow-wrapper {
        height: 260px;
    }

    .slide-nav-left,
    .slide-nav-right {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .slide-nav-left {
        left: 10px;
    }

    .slide-nav-right {
        right: 10px;
    }

    .slide-indicators-internal {
        bottom: 12px;
    }

    .indicator-internal {
        width: 4px;
        height: 4px;
    }

    .highlight-box {
        padding: 20px;
        border-radius: 6px;
        background: transparent; /* Weißer/transparenter Hintergrund */
        box-shadow: none; /* Schatten entfernt */
    }

    .highlight-box strong {
        font-size: 15px;
        margin-bottom: 8px;
        color: #29303b !important; /* Dunkelblauer Text */
    }

    .highlight-box p {
        font-size: 14px;
        line-height: 1.5;
        color: #29303b !important; /* Dunkelblauer Text */
    }

    /* Zwischenmenschlichkeit Section für sehr kleine Bildschirme */
    .zwischenmenschlichkeit-image-section {
        min-height: 300px;
        padding: 15px 10px;
    }

    .zwischenmenschlichkeit-horizontal-image {
        height: 180px; /* +20% von 150px - gleich wie event-transition-image */
        margin-bottom: 15px;
    }

    .zwischenmenschlichkeit-title {
        font-size: 1.3rem;
        padding: 0;
        margin: 0 5px;
        line-height: 1.2;
    }

    .zwischenmenschlichkeit-content-section {
        padding: 15px 10px 30px 10px;
    }

    .events-text {
        margin: 10px;
        padding: 0 15px;
    }

    .banner {
        height: 250px;
    }

    /* Enhanced mobile banner positioning for very small screens */
    .banner.banner-zusammenhalt {
        background-size: 220%;
        background-position: center 50%;
    }

    /* Mobile Hero Banner für kleine Bildschirme - Vollbild */
    .banner-zukunftinsjetzt {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }

    .mobile-hero-title {
        font-size: clamp(1rem, 5vw, 1.8rem) !important;
        color: #fbd19a !important;
        letter-spacing: clamp(0.2px, 0.4vw, 1px) !important;
        margin: 0 15px 5px 15px !important;
        padding: 0 5px !important;
        width: calc(100% - 30px) !important;
    }

    .banner-zukunftinsjetzt::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    /* Mobile Event Banner für kleine Bildschirme - Vollbild */
    .banner-event {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }

    /* Scroll Indicator Mobile Anpassungen */
    .scroll-indicator {
        bottom: 20px;
    }

    .arrow-symbol {
        font-size: 24px;
    }

    .banner-event::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    /* Mobile Jana Banner für kleine Bildschirme - Vollbild */
    .banner-jana {
        height: 100vh !important;
        background-size: cover !important;
        background-position: center !important;
    }

    .banner-jana::after {
        font-size: 0.9rem !important;
        top: 53% !important;
        width: calc(100% - 30px) !important;
        padding: 0 15px !important;
        letter-spacing: 0.3px !important;
    }

    .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 12px !important;
    }

    /* Small mobile responsiveness for letter closing banner */
    .letter-closing-banner {
        height: 400px; /* Mehr Höhe für sehr kleine Bildschirme */
    }

    /* Runder Favicon entfernt */

    .letter-closing-banner {
        padding: 40px 15px; /* Normales Padding ohne Favicon */
    }

    .letter-closing-banner .closing-content {
        gap: 25px;
        padding: 0;
    }

    .letter-closing-banner .closing-profile-image {
        width: 110px;
        height: 110px;
    }

    .letter-closing-banner .closing-text .letter-signature {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .letter-closing-banner .closing-text h3 {
        font-size: 16px;
        margin-bottom: 15px;
    }

    /* About Me Link - small mobile */
    .letter-closing-banner .about-me-link {
        font-size: 11px;
        margin: 6px 0 10px 0;
    }

    .letter-closing-banner .closing-text .social-links {
        gap: 6px;
        margin-bottom: 15px; /* Abstand zum unteren Rand */
    }

    .letter-closing-banner .closing-text .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 10px !important; /* Kleinere Buttons */
    }

    /* Mobile optimizations for flip cards */
    .flip-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin: 15px 0;
    }

    .flip-card-compact {
        height: 140px;
    }

    .flip-card-front, .flip-card-back {
        padding: 10px;
    }

    .flip-card-front h4 {
        font-size: 9px;
        line-height: 1.0;
    }

    .flip-card-back p {
        font-size: 11px;
        line-height: 1.2;
        -webkit-line-clamp: 7;
        line-clamp: 7;
    }
}

/* About Page Styles */
.about-section {
    padding: 24px 20px;
    margin: 0;
    width: 100%;
    text-align: center;
    position: relative;
}

.about-section-white {
    background-color: #fff8ef;
}

.about-section-black {
    background-color: #04070f !important;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-content p {
    margin-bottom: 12px;
}

.about-section-white .about-content p {
    color: #04070f;
    font-size: 16px;
    line-height: 1.8;
}

.about-section-black .about-content p {
    color: #fff8ef;
    font-size: 16px;
    line-height: 1.8;
}

.about-section-white .about-content strong {
    color: #04070f;
}

.about-section-black .about-content strong {
    color: #fbc99a;
}

.about-section-white .about-content a {
    color: #04070f;
    text-decoration: underline;
}

.about-section-black .about-content a {
    color: #fbc99a;
    text-decoration: underline;
}

/* H1 Titel Farben für About-Sektionen */
.about-section-white .event-main-title {
    color: #fbc99a; /* Pfirsichfarbe für weiße Hintergründe */
}

.about-section-black .event-main-title {
    color: #fbc99a; /* Pfirsichfarbe für schwarze Hintergründe */
}

/* Event Page Styles - Much smaller spacing! */
.event-section {
    padding: 24px 20px; /* Noch kleiner! */
    margin: 0;
    width: 100%;
    text-align: center;
    position: relative;
}

.event-section-white {
    background-color: #fff8ef;
}

.event-section-black {
    background-color: #04070f;
}

.event-section-pink {
    background-color: #870833;
}

/* Rundes Favicon-Bild nur für den letzten Banner */
.transition-image {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    z-index: 10;
}

.event-main-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 auto 12px auto; /* Viel kleiner! */
    max-width: 800px;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.event-section-white .event-main-title {
    color: #fbc99a; /* Pfirsichfarbe für weiße Hintergründe */
}

.event-section-black .event-main-title {
    color: #fbc99a; /* Pfirsichfarbe für schwarze Hintergründe */
}

.event-section-pink .event-main-title {
    color: #fff8ef; /* Weiße Farbe für pinke Hintergründe */
}

.event-content {
    max-width: 800px;
    margin: 0 auto;
}

.event-content p {
    margin-bottom: 12px; /* Kleiner! */
}

/* Transition Container für Bilder zwischen Sektionen */
.event-transition-container {
    position: relative;
    width: 100%;
    height: 360px; /* +20% von 300px */
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.event-transition-image {
    width: 100%;
    max-width: 1120px; /* +40% von 800px für Desktop */
    height: 360px; /* +20% von 300px */
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 10;
    position: relative;
}

/* Wechsel zwischen White zu Black */
.event-section-white + .event-transition-container,
.about-section-white + .event-transition-container {
    background: linear-gradient(to bottom, #fff8ef 50%, #04070f 50%);
}

/* Wechsel zwischen Black zu White */
.event-section-black + .event-transition-container,
.about-section-black + .event-transition-container {
    background: linear-gradient(to bottom, #04070f 50%, #fff8ef 50%);
}

/* Wechsel zwischen White zu Pink - spezifische Klasse für Transition zu Pink */
.event-transition-to-pink {
    background: linear-gradient(to bottom, #fff8ef 50%, #870833 50%) !important;
}

/* Wechsel zwischen Dark Blue zu Pink - für Startseite nach Zwischenmenschlichkeit Section */
.zwischenmenschlichkeit-content-section ~ .event-transition-to-pink {
    background: linear-gradient(to bottom, #04070f 50%, #870833 50%) !important;
}

/* Wechsel zwischen Black zu Pink - für Event-Seite nach "In meinen kühnsten Träumen" Section */
.event-section-black + .event-transition-to-pink {
    background: linear-gradient(to bottom, #04070f 50%, #870833 50%) !important;
}

/* Wechsel zwischen About White zu Pink - für Über-mich-Seite */
.about-section-white + .event-transition-to-pink {
    background: linear-gradient(to bottom, #fff8ef 50%, #870833 50%) !important;
}

/* Wechsel zwischen About Black zu Pink - für Über-mich-Seite */
.about-section-black + .event-transition-to-pink {
    background: linear-gradient(to bottom, #04070f 50%, #870833 50%) !important;
}

/* Rundes Favicon-Bild nur für den letzten Banner - ursprüngliche Größe */

.event-section-white .event-content p {
    color: #04070f;
}

.event-section-black .event-content p {
    color: #fff8ef;
}

.event-section-pink .event-content p {
    color: #fff8ef;
}

.event-section-white .event-content strong {
    color: #04070f;
}

.event-section-black .event-content strong {
    color: #fff8ef;
}

.event-section-pink .event-content strong {
    color: #fff8ef;
}

/* Listen in Event Sections */
.event-section .possibilities-list,
.event-section .facts-list {
    display: inline-block;
    text-align: left;
    max-width: 600px;
    margin: 6px auto; /* Noch viel kleiner! */
}

.event-section-white .possibilities-list li,
.event-section-white .facts-list li {
    color: #04070f;
}

.event-section-black .possibilities-list li,
.event-section-black .facts-list li {
    color: #fff8ef;
}

.event-section-white .possibilities-list li::before {
    color: #fbc99a; /* Pfirsich für Sterne */
}

.event-section-black .possibilities-list li::before {
    color: #fbc99a; /* Pfirsich für Sterne */
}

.event-section-white .facts-list li::before {
    color: #04070f; /* Dunkelblau für Pfeile */
}

.event-section-black .facts-list li::before {
    color: #fff8ef; /* Weiß für Pfeile */
}

/* Modern Accordion in Event Sections - Consistent Dark Theme */
.event-section-white .accordion-container,
.event-section-black .accordion-container {
    background: transparent;
}

/* Beide Sections verwenden jetzt das gleiche elegante Design */
.event-section-white .accordion-item,
.event-section-black .accordion-item {
    background: rgba(41, 48, 59, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.event-section-white .accordion-item:hover,
.event-section-black .accordion-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 40px rgba(251, 201, 154, 0.15);
    border-color: rgba(251, 201, 154, 0.3);
}

.event-section-white .accordion-header,
.event-section-black .accordion-header {
    background: transparent;
}

.event-section-white .accordion-header:hover,
.event-section-black .accordion-header:hover {
    background: rgba(251, 201, 154, 0.05);
}

.event-section-white .accordion-header.active,
.event-section-black .accordion-header.active {
    background: rgba(251, 201, 154, 0.08);
}

.event-section-white .day-number,
.event-section-black .day-number {
    color: #fbc99a;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.event-section-white .day-focus,
.event-section-black .day-focus {
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.event-section-white .accordion-icon,
.event-section-black .accordion-icon {
    color: #fbc99a;
    min-width: 25px;
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
}

.event-section-white .accordion-header.active .accordion-icon,
.event-section-black .accordion-header.active .accordion-icon {
    transform: translateY(-50%) rotate(45deg);
}

.event-section-white .accordion-content,
.event-section-black .accordion-content {
    background: rgba(15, 21, 26, 0.8);
    backdrop-filter: blur(5px);
}

.event-section-white .accordion-content p,
.event-section-black .accordion-content p {
    color: #e8e8e8;
    border-top: 1px solid rgba(251, 201, 154, 0.2);
}

/* Peach Card in Event Sections */
.event-section-black .peach-card {
    background-color: rgba(251, 217, 154, 0.15);
    border: 1px solid rgba(251, 217, 154, 0.3);
    color: #fff8ef;
}

/* Mobile Responsive für About Page */
@media (max-width: 768px) {
    .about-section {
        padding: 24px 15px;
    }
}

/* Mobile Responsive für Event Page */
@media (max-width: 768px) {
    .event-section {
        padding: 24px 15px; /* Viel kleiner! */
    }

    .event-main-title {
        font-size: 1.8rem;
        margin: 0 auto 20px auto; /* Viel kleiner! */
    }

    .transition-image {
        width: 60px;
        height: 60px;
        top: -30px;
    }

    .event-transition-container {
        height: 240px; /* +20% von 200px */
    }

    .event-transition-image {
        height: 240px; /* +20% von 200px */
        max-width: 85%; /* Bild schmaler machen für sichtbaren Farbwechsel */
    }

    /* Slideshow Mobile Optimierung für Event Page */
    .slideshow-container {
        margin: 12px 8px; /* Kleiner! */
        max-width: calc(100vw - 32px);
        border-radius: 8px;
    }

    .slideshow-wrapper {
        height: 320px; /* Mehr Höhe für bessere Lesbarkeit */
    }

    .highlight-box {
        padding: 16px; /* Konsistent */
        border-radius: 8px;
        font-size: 14px;
        background: transparent; /* Weißer/transparenter Hintergrund */
        box-shadow: none; /* Schatten entfernt */
    }

    .highlight-box p {
        line-height: 1.6;
        color: #29303b !important; /* Dunkelblauer Text */
    }

    .highlight-box strong {
        color: #29303b !important; /* Dunkelblauer Text */
    }

    .slide-nav-left,
    .slide-nav-right {
        width: 36px;
        height: 36px;
        font-size: 16px;
        background: transparent;
        border: 1px solid #29303b;
        color: #29303b;
    }

    .slide-nav-left {
        left: -50px; /* Außerhalb des Slide-Kästchens */
    }

    .slide-nav-right {
        right: -50px; /* Außerhalb des Slide-Kästchens */
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        background: #29303b;
        color: #fff8ef;
    }

    /* Akkordeon Mobile Optimierung für Event Page */
    .accordion-container {
        margin: 12px 8px; /* Kleiner! */
        max-width: calc(100vw - 32px);
    }

    .accordion-header {
        padding: 12px; /* Kleiner! */
        flex-wrap: wrap;
    }

    .day-number {
        min-width: auto;
        font-size: 14px;
        letter-spacing: 1px;
        margin-bottom: 6px; /* Kleiner! */
        width: 100%;
    }

    .day-focus {
        margin-left: 0;
        font-size: 14px;
        flex: 1;
    }

    .accordion-icon {
        font-size: 16px;
        min-width: 20px;
    }

    .accordion-content p {
        padding: 12px; /* Kleiner! */
        font-size: 14px;
        line-height: 1.6;
    }

    .accordion-content.active {
        max-height: 300px; /* Mehr Platz für längere Texte */
    }


}

@media (max-width: 480px) {
    .about-section {
        padding: 20px 10px;
    }

    .event-section {
        padding: 20px 10px; /* Viel kleiner! */
    }

    .event-main-title {
        font-size: 1.5rem;
        margin: 0 auto 16px auto; /* Viel kleiner! */
    }

    .transition-image {
        width: 50px;
        height: 50px;
        top: -25px;
    }

    .event-transition-container {
        height: 180px; /* +20% von 150px */
    }

    .event-transition-image {
        height: 180px; /* +20% von 150px */
        max-width: 80%; /* Noch schmaler für sehr kleine Bildschirme */
    }

    /* Slideshow für sehr kleine Bildschirme - Event Page */
    .slideshow-container {
        margin: 10px 2px;
        max-width: calc(100vw - 20px);
        border-radius: 6px;
    }

    .slideshow-wrapper {
        height: 300px; /* Ausreichend Höhe für Lesbarkeit */
    }

    .highlight-box {
        padding: 15px 10px;
        border-radius: 6px;
        font-size: 13px;
        background: transparent; /* Weißer/transparenter Hintergrund */
        box-shadow: none; /* Schatten entfernt */
    }

    .highlight-box strong {
        font-size: 14px;
        color: #29303b !important; /* Dunkelblauer Text */
    }

    .highlight-box p {
        color: #29303b !important; /* Dunkelblauer Text */
    }

    .slide-nav-left,
    .slide-nav-right {
        width: 32px;
        height: 32px;
        font-size: 14px;
        background: transparent;
        border: 1px solid #29303b;
        color: #29303b;
    }

    .slide-nav-left {
        left: -45px; /* Außerhalb des Slide-Kästchens */
    }

    .slide-nav-right {
        right: -45px; /* Außerhalb des Slide-Kästchens */
    }

    .slide-nav-left:hover,
    .slide-nav-right:hover {
        background: #29303b;
        color: #fff8ef;
    }

    .slide-indicators-internal {
        bottom: 10px;
    }

    .indicator-internal {
        width: 4px;
        height: 4px;
        background: rgba(41, 48, 59, 0.3); /* Dunkelblau */
    }

    .indicator-internal.active {
        background: #29303b; /* Dunkelblau */
    }

    .indicator-internal:hover:not(.active) {
        background: rgba(41, 48, 59, 0.6); /* Dunkelblau */
    }

    /* Akkordeon für sehr kleine Bildschirme - Event Page */
    .accordion-container {
        margin: 10px 2px;
        max-width: calc(100vw - 20px);
    }

    .accordion-header {
        padding: 12px 8px;
    }

    .day-number {
        font-size: 12px;
        letter-spacing: 0.5px;
    }

    .day-focus {
        font-size: 13px;
    }

    .accordion-icon {
        font-size: 14px;
        min-width: 18px;
    }

    .accordion-content p {
        padding: 12px 8px;
        font-size: 13px;
        line-height: 1.5;
    }

    .accordion-content.active {
        max-height: 350px; /* Noch mehr Platz für sehr kleine Bildschirme */
    }


}